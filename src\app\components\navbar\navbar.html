<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
  <a class="navbar-brand" routerLink="/">Navbar</a>

  <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
          data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
          aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>

  <div class="collapse navbar-collapse" id="navbarSupportedContent">
    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
      <li class="nav-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }">
        <a class="nav-link" routerLink="/">
          Home <span class="visually-hidden">(current)</span>
        </a>
      </li>

      <li class="nav-item" routerLinkActive="active">
        <a class="nav-link" routerLink="/link">Link</a>
      </li>

      <li class="nav-item dropdown" routerLinkActive="active">
        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button"
           data-bs-toggle="dropdown" aria-expanded="false">
          Dropdown
        </a>
        <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
          <li><a class="dropdown-item" routerLink="/action">Action</a></li>
          <li><a class="dropdown-item" routerLink="/another">Another action</a></li>
          <!--<li><hr class="dropdown-divider"></li>-->
          <li><a class="dropdown-item" routerLink="/something-else">Something else here</a></li>
        </ul>
      </li>

      <li class="nav-item">
        <a class="nav-link disabled" tabindex="-1" aria-disabled="true">Disabled</a>
      </li>
    </ul>

    <form class="d-flex" role="search">
      <input class="form-control me-2" type="search" placeholder="Search" aria-label="Search">
      <button class="btn btn-outline-success" type="submit">Search</button>
    </form>
  </div>
</nav>
